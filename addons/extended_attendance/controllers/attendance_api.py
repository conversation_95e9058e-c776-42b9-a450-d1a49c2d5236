import json
import logging
from datetime import datetime, timedelta
from odoo import http, fields, _
from odoo.http import request
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)


class ExtendedAttendanceAPI(http.Controller):
    """REST API Controller for Extended Attendance System"""

    def _get_response(self, success=True, data=None, message=None, error=None):
        """Standard response format"""
        response = {
            'success': success,
            'timestamp': fields.Datetime.now().isoformat(),
        }
        
        if data is not None:
            response['data'] = data
        if message:
            response['message'] = message
        if error:
            response['error'] = error
            
        return response

    def _handle_exception(self, e):
        """Handle exceptions and return appropriate response"""
        _logger.error(f"API Error: {str(e)}")
        
        if isinstance(e, (ValidationError, UserError)):
            return self._get_response(success=False, error=str(e))
        else:
            return self._get_response(success=False, error="Internal server error")

    # Person Types API
    @http.route('/api/attendance/person-types', type='json', auth='user', methods=['GET'])
    def get_person_types(self):
        """Get all person types"""
        try:
            person_types = request.env['extended.attendance.person.type'].search([])
            data = []
            
            for pt in person_types:
                data.append({
                    'id': pt.id,
                    'name': pt.name,
                    'code': pt.code,
                    'description': pt.description,
                    'sequence': pt.sequence,
                    'active': pt.active,
                    'is_system': pt.is_system,
                    'can_delete': pt.can_delete,
                    'person_count': pt.person_count,
                    'requires_approval': pt.requires_approval,
                    'default_access_level': pt.default_access_level,
                    'max_duration_hours': pt.max_duration_hours,
                })
            
            return self._get_response(data=data)
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/person-types', type='json', auth='user', methods=['POST'])
    def create_person_type(self, **kwargs):
        """Create a new person type"""
        try:
            required_fields = ['name', 'code']
            for field in required_fields:
                if field not in kwargs:
                    return self._get_response(success=False, error=f"Missing required field: {field}")
            
            person_type = request.env['extended.attendance.person.type'].create(kwargs)
            
            return self._get_response(
                data={'id': person_type.id, 'name': person_type.name},
                message=f"Person type '{person_type.name}' created successfully"
            )
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/person-types/<int:type_id>', type='json', auth='user', methods=['PUT'])
    def update_person_type(self, type_id, **kwargs):
        """Update a person type"""
        try:
            person_type = request.env['extended.attendance.person.type'].browse(type_id)
            if not person_type.exists():
                return self._get_response(success=False, error="Person type not found")
            
            person_type.write(kwargs)
            
            return self._get_response(
                data={'id': person_type.id, 'name': person_type.name},
                message=f"Person type '{person_type.name}' updated successfully"
            )
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/person-types/<int:type_id>', type='json', auth='user', methods=['DELETE'])
    def delete_person_type(self, type_id):
        """Delete a person type"""
        try:
            person_type = request.env['extended.attendance.person.type'].browse(type_id)
            if not person_type.exists():
                return self._get_response(success=False, error="Person type not found")
            
            name = person_type.name
            person_type.unlink()
            
            return self._get_response(message=f"Person type '{name}' deleted successfully")
            
        except Exception as e:
            return self._handle_exception(e)

    # Locations API
    @http.route('/api/attendance/locations', type='json', auth='user', methods=['GET'])
    def get_locations(self):
        """Get all locations"""
        try:
            locations = request.env['extended.attendance.location'].search([])
            data = []
            
            for loc in locations:
                data.append({
                    'id': loc.id,
                    'name': loc.name,
                    'code': loc.code,
                    'description': loc.description,
                    'sequence': loc.sequence,
                    'active': loc.active,
                    'building': loc.building,
                    'floor': loc.floor,
                    'room_number': loc.room_number,
                    'capacity': loc.capacity,
                    'current_occupancy': loc.current_occupancy,
                    'parent_location_id': loc.parent_location_id.id if loc.parent_location_id else None,
                    'parent_location_name': loc.parent_location_id.name if loc.parent_location_id else None,
                    'location_path': loc.location_path,
                    'requires_permission': loc.requires_permission,
                    'has_operating_hours': loc.has_operating_hours,
                    'is_operating_now': loc.is_operating_now(),
                })
            
            return self._get_response(data=data)
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/locations', type='json', auth='user', methods=['POST'])
    def create_location(self, **kwargs):
        """Create a new location"""
        try:
            required_fields = ['name', 'code']
            for field in required_fields:
                if field not in kwargs:
                    return self._get_response(success=False, error=f"Missing required field: {field}")
            
            location = request.env['extended.attendance.location'].create(kwargs)
            
            return self._get_response(
                data={'id': location.id, 'name': location.name, 'code': location.code},
                message=f"Location '{location.name}' created successfully"
            )
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/locations/<int:location_id>', type='json', auth='user', methods=['PUT'])
    def update_location(self, location_id, **kwargs):
        """Update a location"""
        try:
            location = request.env['extended.attendance.location'].browse(location_id)
            if not location.exists():
                return self._get_response(success=False, error="Location not found")
            
            location.write(kwargs)
            
            return self._get_response(
                data={'id': location.id, 'name': location.name},
                message=f"Location '{location.name}' updated successfully"
            )
            
        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/locations/<int:location_id>', type='json', auth='user', methods=['DELETE'])
    def delete_location(self, location_id):
        """Delete a location"""
        try:
            location = request.env['extended.attendance.location'].browse(location_id)
            if not location.exists():
                return self._get_response(success=False, error="Location not found")
            
            name = location.name
            location.unlink()
            
            return self._get_response(message=f"Location '{name}' deleted successfully")
            
        except Exception as e:
            return self._handle_exception(e)

    # Persons API
    @http.route('/api/attendance/persons', type='json', auth='user', methods=['GET'])
    def get_persons(self, **kwargs):
        """Get all persons with optional filtering"""
        try:
            domain = []
            
            # Add filters
            if kwargs.get('person_type_code'):
                person_type = request.env['extended.attendance.person.type'].search([
                    ('code', '=', kwargs['person_type_code'])
                ], limit=1)
                if person_type:
                    domain.append(('person_type_id', '=', person_type.id))
            
            if kwargs.get('active') is not None:
                domain.append(('active', '=', kwargs['active']))
            
            persons = request.env['extended.attendance.person'].search(domain)
            data = []
            
            for person in persons:
                data.append({
                    'id': person.id,
                    'name': person.name,
                    'person_id': person.person_id,
                    'person_type': {
                        'id': person.person_type_id.id,
                        'name': person.person_type_id.name,
                        'code': person.person_type_id.code,
                    },
                    'email': person.email,
                    'phone': person.phone,
                    'active': person.active,
                    'access_level': person.access_level,
                    'is_checked_in': person.is_checked_in,
                    'current_location': {
                        'id': person.current_location_id.id,
                        'name': person.current_location_id.name,
                        'code': person.current_location_id.code,
                    } if person.current_location_id else None,
                    'last_attendance': person.last_attendance.isoformat() if person.last_attendance else None,
                    'attendance_count': person.attendance_count,
                })
            
            return self._get_response(data=data)

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons', type='json', auth='user', methods=['POST'])
    def create_person(self, **kwargs):
        """Create a new person"""
        try:
            required_fields = ['name', 'person_id', 'person_type_id']
            for field in required_fields:
                if field not in kwargs:
                    return self._get_response(success=False, error=f"Missing required field: {field}")

            person = request.env['extended.attendance.person'].create(kwargs)

            return self._get_response(
                data={'id': person.id, 'name': person.name, 'person_id': person.person_id},
                message=f"Person '{person.name}' created successfully"
            )

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons/<int:person_id>', type='json', auth='user', methods=['PUT'])
    def update_person(self, person_id, **kwargs):
        """Update a person"""
        try:
            person = request.env['extended.attendance.person'].browse(person_id)
            if not person.exists():
                return self._get_response(success=False, error="Person not found")

            person.write(kwargs)

            return self._get_response(
                data={'id': person.id, 'name': person.name},
                message=f"Person '{person.name}' updated successfully"
            )

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons/<int:person_id>', type='json', auth='user', methods=['DELETE'])
    def delete_person(self, person_id):
        """Delete a person"""
        try:
            person = request.env['extended.attendance.person'].browse(person_id)
            if not person.exists():
                return self._get_response(success=False, error="Person not found")

            name = person.name
            person.unlink()

            return self._get_response(message=f"Person '{name}' deleted successfully")

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/persons/search', type='json', auth='user', methods=['POST'])
    def search_person(self, identifier):
        """Search person by identifier"""
        try:
            person = request.env['extended.attendance.person'].search_by_identifier(identifier)

            if not person:
                return self._get_response(success=False, error=f"Person not found with identifier: {identifier}")

            data = {
                'id': person.id,
                'name': person.name,
                'person_id': person.person_id,
                'person_type': {
                    'id': person.person_type_id.id,
                    'name': person.person_type_id.name,
                    'code': person.person_type_id.code,
                },
                'active': person.active,
                'is_checked_in': person.is_checked_in,
                'current_location': {
                    'id': person.current_location_id.id,
                    'name': person.current_location_id.name,
                    'code': person.current_location_id.code,
                } if person.current_location_id else None,
            }

            return self._get_response(data=data)

        except Exception as e:
            return self._handle_exception(e)

    # Attendance API
    @http.route('/api/attendance/check-in', type='json', auth='user', methods=['POST'])
    def check_in(self, person_identifier, location_code, device_id=None, check_in_time=None):
        """Check in a person at a location"""
        try:
            # Parse check_in_time if provided
            if check_in_time:
                check_in_time = fields.Datetime.from_string(check_in_time)

            attendance = request.env['extended.attendance.record'].create_check_in(
                person_identifier, location_code, device_id, check_in_time
            )

            data = {
                'id': attendance.id,
                'person_name': attendance.person_name,
                'location_name': attendance.location_name,
                'check_in': attendance.check_in.isoformat(),
                'state': attendance.state,
            }

            return self._get_response(
                data=data,
                message=f"Successfully checked in {attendance.person_name} at {attendance.location_name}"
            )

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/check-out', type='json', auth='user', methods=['POST'])
    def check_out(self, person_identifier, check_out_time=None):
        """Check out a person"""
        try:
            # Parse check_out_time if provided
            if check_out_time:
                check_out_time = fields.Datetime.from_string(check_out_time)

            attendance = request.env['extended.attendance.record'].create_check_out(
                person_identifier, check_out_time
            )

            data = {
                'id': attendance.id,
                'person_name': attendance.person_name,
                'location_name': attendance.location_name,
                'check_in': attendance.check_in.isoformat(),
                'check_out': attendance.check_out.isoformat(),
                'worked_hours': attendance.worked_hours,
                'duration_display': attendance.duration_display,
                'state': attendance.state,
            }

            return self._get_response(
                data=data,
                message=f"Successfully checked out {attendance.person_name} from {attendance.location_name}"
            )

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/current', type='json', auth='user', methods=['GET'])
    def get_current_attendance(self, location_code=None):
        """Get current attendance (people currently checked in)"""
        try:
            attendances = request.env['extended.attendance.record'].get_current_attendance(location_code)
            data = []

            for att in attendances:
                data.append({
                    'id': att.id,
                    'person_name': att.person_name,
                    'person_id': att.person_id.person_id,
                    'person_type': att.person_type_id.name,
                    'location_name': att.location_name,
                    'location_code': att.location_id.code,
                    'check_in': att.check_in.isoformat(),
                    'duration_display': att.duration_display,
                })

            return self._get_response(data=data)

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/records', type='json', auth='user', methods=['GET'])
    def get_attendance_records(self, **kwargs):
        """Get attendance records with filtering"""
        try:
            domain = []

            # Date filtering
            if kwargs.get('date_from'):
                domain.append(('check_in', '>=', kwargs['date_from']))
            if kwargs.get('date_to'):
                domain.append(('check_in', '<=', kwargs['date_to']))

            # Location filtering
            if kwargs.get('location_code'):
                location = request.env['extended.attendance.location'].search([
                    ('code', '=', kwargs['location_code'])
                ], limit=1)
                if location:
                    domain.append(('location_id', '=', location.id))

            # Person type filtering
            if kwargs.get('person_type_code'):
                person_type = request.env['extended.attendance.person.type'].search([
                    ('code', '=', kwargs['person_type_code'])
                ], limit=1)
                if person_type:
                    domain.append(('person_type_id', '=', person_type.id))

            # Person filtering
            if kwargs.get('person_identifier'):
                person = request.env['extended.attendance.person'].search_by_identifier(
                    kwargs['person_identifier']
                )
                if person:
                    domain.append(('person_id', '=', person.id))

            # Pagination
            limit = kwargs.get('limit', 100)
            offset = kwargs.get('offset', 0)

            records = request.env['extended.attendance.record'].search(
                domain, limit=limit, offset=offset, order='check_in desc'
            )

            data = []
            for record in records:
                data.append({
                    'id': record.id,
                    'person_name': record.person_name,
                    'person_id': record.person_id.person_id,
                    'person_type': record.person_type_id.name,
                    'location_name': record.location_name,
                    'location_code': record.location_id.code,
                    'check_in': record.check_in.isoformat(),
                    'check_out': record.check_out.isoformat() if record.check_out else None,
                    'worked_hours': record.worked_hours,
                    'duration_display': record.duration_display,
                    'state': record.state,
                    'is_overtime': record.is_overtime,
                })

            return self._get_response(data=data)

        except Exception as e:
            return self._handle_exception(e)

    @http.route('/api/attendance/report', type='json', auth='user', methods=['POST'])
    def get_attendance_report(self, date_from, date_to, location_code=None, person_type_code=None):
        """Generate attendance report"""
        try:
            report_data = request.env['extended.attendance.record'].get_attendance_report(
                date_from, date_to, location_code, person_type_code
            )

            # Convert records to serializable format
            records_data = []
            for record in report_data['records']:
                records_data.append({
                    'id': record.id,
                    'person_name': record.person_name,
                    'person_type': record.person_type_id.name,
                    'location_name': record.location_name,
                    'check_in': record.check_in.isoformat(),
                    'check_out': record.check_out.isoformat() if record.check_out else None,
                    'worked_hours': record.worked_hours,
                    'state': record.state,
                })

            data = {
                'records': records_data,
                'statistics': report_data['statistics']
            }

            return self._get_response(data=data)

        except Exception as e:
            return self._handle_exception(e)
