<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- FastAPI Endpoint Configuration -->
        <record id="fastapi_endpoint_attendance" model="fastapi.endpoint">
            <field name="name">Extended Attendance API</field>
            <field name="path">/attendance-api</field>
            <field name="app">extended_attendance.fastapi_endpoints.app:app</field>
            <field name="user_id" ref="base.user_admin"/>
            <field name="demo_user_id" ref="base.user_admin"/>
        </record>
    </data>
</odoo>
